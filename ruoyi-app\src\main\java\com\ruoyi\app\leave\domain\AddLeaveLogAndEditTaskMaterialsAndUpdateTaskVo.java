package com.ruoyi.app.leave.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

@Data
public class AddLeaveLogAndEditTaskMaterialsAndUpdateTaskVo extends BaseEntity {

    private List<LeaveTaskMaterial> taskMaterialList;

    private LeaveTask leaveTask;

    private Integer measureFlag;

    /**
     * 客户端IP地址（用于识别门号）
     */
    private String clientIp;

    /**
     * 日志信息（用于后端创建LeaveLog）
     */
    private String logInfo;

    private LeaveLog leaveLog;
}
