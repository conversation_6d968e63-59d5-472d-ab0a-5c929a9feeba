{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=template&id=4b5796a9&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1756454715530}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}