{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue?vue&type=style&index=1&id=c9a9bf16&lang=scss", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue", "mtime": 1756449668568}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZGlzcGF0Y2gtbG9nLWRpYWxvZyAuZWwtZGlhbG9nX19ib2R5IHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLmVsLXRhYmxlIHsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KDQogIHRoIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhICFpbXBvcnRhbnQ7DQogICAgY29sb3I6ICM2MDYyNjY7DQogICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIH0NCg0KICB0ZCB7DQogICAgcGFkZGluZzogMTJweCAwOw0KICB9DQp9DQoNCi5lbC10aW1lbGluZSB7DQogIHBhZGRpbmc6IDIwcHggIWltcG9ydGFudDsNCg0KICAuZWwtdGltZWxpbmUtaXRlbV9fbm9kZSB7DQogICAgd2lkdGg6IDEycHg7DQogICAgaGVpZ2h0OiAxMnB4Ow0KICB9DQoNCiAgLmVsLXRpbWVsaW5lLWl0ZW1fX2NvbnRlbnQgew0KICAgIHBhZGRpbmc6IDAgMCAwIDBweDsNCiAgfQ0KfQ0KDQouZWwtZGVzY3JpcHRpb25zIHsNCiAgLmVsLWRlc2NyaXB0aW9ucy1pdGVtX19sYWJlbCB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsNCiAgfQ0KfQ0KDQouZWwtdGFnIHsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgcGFkZGluZzogMCAxMHB4Ow0KfQ0KDQouZWwtYnV0dG9uLS10ZXh0IHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIHBhZGRpbmctbGVmdDogMDsNCiAgcGFkZGluZy1yaWdodDogMDsNCg0KICAmOmhvdmVyIHsNCiAgICBjb2xvcjogIzY2YjFmZjsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsNCiAgfQ0KDQogICY6Zm9jdXMgew0KICAgIGNvbG9yOiAjNDA5RUZGOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA86DA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>申请详情</h3>\r\n      </div>\r\n\r\n      <!-- 基本信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">基本信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"申请编号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 申请编号</template>\r\n            {{ planInfo.applyNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划号</template>\r\n            {{ planInfo.planNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划状态\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-flag\"></i> 计划状态</template>\r\n            <el-tag :type=\"getPlanStatusType(planInfo.planStatus)\">{{ getPlanStatusText(planInfo.planStatus) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"计划类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-order\"></i> 计划类型</template>\r\n            <el-tag :type=\"getPlanTypeTagType(planInfo.planType)\">{{ getPlanTypeText(planInfo.planType) }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"业务类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-management\"></i> 业务类型</template>\r\n            <el-tag :type=\"getBusinessCategoryTagType(planInfo.businessCategory)\">{{\r\n              getBusinessCategoryText(planInfo.businessCategory) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"是否计量\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-operation\"></i> 是否计量</template>\r\n            <el-tag :type=\"planInfo.measureFlag === 1 ? 'success' : 'danger'\">\r\n              {{ planInfo.measureFlag === 1 ? '计量' : '不计量' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.plannedAmount\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划量（吨）</template>\r\n            {{ planInfo.plannedAmount }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"是否复审\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-check\"></i> 是否复审</template>\r\n            <el-tag :type=\"planInfo.secApproveFlag === 1 ? 'warning' : 'info'\">\r\n              {{ planInfo.secApproveFlag === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请单位\" v-if=\"planInfo.sourceCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 申请单位</template>\r\n            {{ planInfo.sourceCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"收货单位\" v-if=\"planInfo.receiveCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-school\"></i> 收货单位</template>\r\n            {{ planInfo.receiveCompany }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"返回单位\" v-if=\"planInfo.targetCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-home\"></i> 返回单位</template>\r\n            {{ planInfo.targetCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划返回时间\" v-if=\"planInfo.planReturnTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-time\"></i> 计划返回时间</template>\r\n            {{ planInfo.planReturnTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"退货单位\" v-if=\"planInfo.refundDepartment\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-shop\"></i> 退货单位</template>\r\n            {{ planInfo.refundDepartment }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"开始时间\" v-if=\"planInfo.startTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 开始时间</template>\r\n            {{ planInfo.startTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"结束时间\" v-if=\"planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 结束时间</template>\r\n            {{ planInfo.endTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"有效期\" v-if=\"!planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 有效期</template>\r\n            {{ planInfo.expireTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"监装人\" v-if=\"planInfo.monitor\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 监装人</template>\r\n            {{ planInfo.monitor }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"物资专管员\" v-if=\"planInfo.specialManager\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-custom\"></i> 物资专管员</template>\r\n            {{ planInfo.specialManager }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"物资类型\" v-if=\"planInfo.itemType\">\r\n            <template slot=\"label\"><i class=\"el-icon-goods\"></i> 物资类型</template>\r\n            <el-tag :type=\"getMaterialTypeTagType(planInfo.itemType)\">\r\n              {{ getMaterialTypeText(planInfo.itemType) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"出厂原因\" v-if=\"planInfo.reason\">\r\n            <template slot=\"label\"><i class=\"el-icon-info\"></i> 出厂原因</template>\r\n            {{ planInfo.reason }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"合同号\" v-if=\"planInfo.contractNo\">\r\n            <template slot=\"label\"><i class=\"el-icon-tickets\"></i> 合同号</template>\r\n            {{ planInfo.contractNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"申请时间\" v-if=\"planInfo.applyTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-timer\"></i> 申请时间</template>\r\n            {{ planInfo.applyTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请人\" v-if=\"planInfo.applyUserName\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 申请人</template>\r\n            {{ planInfo.applyUserName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.planned_amount\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 计划量(吨)</template>\r\n            {{ planInfo.planned_amount }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 新增图片列表部分 -->\r\n      <div class=\"section-container\" v-if=\"imageList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-picture-outline\"></i> 申请图片</span>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <viewer :images=\"imageList\">\r\n            <div class=\"image-list\">\r\n              <div class=\"image-item\" v-for=\"(image, index) in imageList\" :key=\"'img-' + index\">\r\n                <img :src=\"image.url\" :alt=\"image.name\">\r\n                <div class=\"image-name\">{{ image.name }}</div>\r\n              </div>\r\n            </div>\r\n          </viewer>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 新增文件列表部分 -->\r\n      <div class=\"section-container\" v-if=\"fileList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-document\"></i> 申请附件</span>\r\n        </div>\r\n        <div class=\"file-container\">\r\n          <div class=\"file-list\">\r\n            <div class=\"file-item\" v-for=\"(file, index) in fileList\" :key=\"'file-' + index\"\r\n              @click=\"downloadFile(file.url, file.name)\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <div class=\"file-name\">{{ file.name }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"planInfo.materials\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\" width=\"150\">\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 派车信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">\r\n          <span>派车信息</span>\r\n          <el-button type=\"primary\" size=\"small\" icon=\"el-icon-truck\" @click=\"openDispatchDialog\"\r\n            :disabled=\"!canDispatchCar\" class=\"dispatch-btn\">\r\n            派车\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-table v-if=\"taskListInfo.length > 0\" :data=\"taskListInfo\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"carNum\" label=\"车牌号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"driverName\" label=\"司机姓名\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"mobilePhone\" label=\"司机手机号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\" :formatter=\"taskTypeFormat\">\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0 \">\r\n          </el-table-column>\r\n          <el-table-column prop=\"factoryReceiveNum\" label=\"分厂确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"计量单位\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.tare || '-' }} {{ scope.row.tare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.gross || '-' }} {{ scope.row.gross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secTare || '-' }} {{ scope.row.secTare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secGross || '-' }} {{ scope.row.secGross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n\r\n\r\n          <el-table-column prop=\"createTime\" label=\"派车时间\" width=\"160\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskStatus\" label=\"任务状态\" width=\"120\" :formatter=\"taskStatusFormat\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"goToTaskDetail(scope.row)\">\r\n                任务详情\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <div v-else class=\"empty-data\">\r\n          <el-empty description=\"暂无派车记录\"></el-empty>\r\n        </div>\r\n\r\n        <!-- 物资确认按钮 -->\r\n        <div style=\"text-align: right; margin-top: 15px;\">\r\n          <el-button type=\"primary\" icon=\"el-icon-finished\" @click=\"handleMaterialConfirm\">\r\n            物资确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <!-- v-if=\"canShowMaterialConfirm\" -->\r\n\r\n      <!-- 审核内容部分 -->\r\n      <div class=\"section-container\" v-if=\"planInfo.approveButtonShow\">\r\n        <div class=\"section-title\">审核内容</div>\r\n        <el-form label-width=\"80px\" :model=\"approveForm\" ref=\"approveForm\">\r\n          <el-form-item label=\"审核建议\">\r\n            <el-input type=\"textarea\" v-model=\"approveForm.approveContent\" :rows=\"4\" placeholder=\"请输入审核建议\"\r\n              maxlength=\"200\" show-word-limit></el-input>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px; margin-bottom: 10px;\">审核建议可不填，默认通过为同意，驳回为拒绝\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">日志列表</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in planInfo.leaveLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <!-- 固定底部操作栏 -->\r\n      <div class=\"fixed-bottom-action\">\r\n        <el-row :gutter=\"10\" type=\"flex\" justify=\"center\" align=\"middle\">\r\n          <!-- 返回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\">\r\n            <el-button size=\"medium\" @click=\"cancel\">返回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 通过按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.approveButtonShow\">\r\n            <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-check\" @click=\"handleApprove\">通过</el-button>\r\n          </el-col>\r\n\r\n          <!-- 驳回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.rejectButtonShow\">\r\n            <el-button size=\"medium\" type=\"danger\" icon=\"el-icon-close\" @click=\"handleReject\">驳回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 废弃按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.discardButtonShow\">\r\n            <el-button size=\"medium\" type=\"success\" icon=\"el-icon-delete\" @click=\"handleDiscard\">废弃</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n    </el-card>\r\n\r\n    <!-- 派车弹框 -->\r\n    <el-dialog title=\"派车\" :visible.sync=\"dispatchDialogVisible\" width=\"1200px\" append-to-body destroy-on-close\r\n      @closed=\"resetDispatchForm\">\r\n      <el-form ref=\"dispatchForm\" :model=\"dispatchForm\" :rules=\"dispatchRules\" label-width=\"100px\"\r\n        class=\"dispatch-form\">\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"dispatchForm.name != null\">\r\n          <el-input v-model=\"dispatchForm.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"dispatchForm.phone != null\">\r\n          <el-input v-model=\"dispatchForm.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"dispatchForm.idCard != null\">\r\n          <el-input v-model=\"dispatchForm.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"dispatchForm.photo != null && dispatchForm.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\"\r\n          v-if=\"dispatchForm.driverLicenseImgs != null && dispatchForm.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\"\r\n          v-if=\"dispatchForm.vehicleLicenseImgs != null && dispatchForm.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carUUId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.carUUId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNumber\" v-if=\"dispatchForm.carNumber != null\">\r\n          <el-input v-model=\"dispatchForm.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\"\r\n          v-if=\"dispatchForm.vehicleEmissionStandards != null\">\r\n          <el-select v-model=\"dispatchForm.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled\r\n            style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"任务类型\" prop=\"taskType\" :rules=\"[{ required: true, message: '任务类型不能为空' }]\"\r\n          v-if=\"isTaskTypeEdit == true\">\r\n          <el-select v-model=\"dispatchForm.taskType\" placeholder=\"请选择车任务类型\" style=\"width:300px\">\r\n            <el-option v-for=\"dict in taskTypeOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 新增物资选择表格 -->\r\n        <el-form-item label=\"物资选择\" prop=\"selectedMaterials\"\r\n          v-if=\"planInfo.measureFlag == 0 && dispatchForm.taskType == 2\">\r\n          <el-table :data=\"materialSelectionList\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"序号\"></el-table-column>\r\n            <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select v-model=\"scope.row.materialId\" placeholder=\"请选择物资\"\r\n                  @change=\"handleMaterialChange(scope.row, scope.$index)\">\r\n                  <el-option v-for=\"item in availableMaterials\" :key=\"item.materialId\" :label=\"item.materialName\"\r\n                    :value=\"item.materialId\" :disabled=\"isMaterialAvailable(item)\">\r\n\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"materialSpec\" label=\"物资规格\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.materialSpec }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.planNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remainingNum\" label=\"剩余数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.remainingNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"currentNum\" label=\"本次数量\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.currentNum\" :min=\"0\"\r\n                  @change=\"handleNumChange($event, scope.$index)\" :disabled=\"!scope.row.materialId\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"removeMaterial(scope.$index)\"\r\n                  :disabled=\"materialSelectionList.length === 1\">\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-top: 10px;\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addMaterialRow\">添加物资</el-button>\r\n          </div>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dispatchDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitDispatchForm\">确 认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { detailPlan, approve, discard, listTaskMaterial, confirmMaterial } from \"@/api/leave/plan\";\r\nimport { listAllTask, addTask, addTaskMaterial, addTaskAndMaterial, addLeaveLog, isAllowDispatch, addTaskAndMaterialAndAddLeaveLog } from \"@/api/leave/task\";\r\nimport { listAllDriver,  getXctgDriverUserListByPage, getXctgDriverCarListByPage } from \"@/api/dgcb/driver/driver\";\r\nimport { mount } from \"sortablejs\";\r\nexport default {\r\n  name: \"DetailLeavePlan\",\r\n  data() {\r\n    // 验证车牌号\r\n    const validateCarNumber = (rule, value, callback) => {\r\n      const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的车牌号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证手机号\r\n    const validatePhone = (rule, value, callback) => {\r\n      const pattern = /^1[3-9]\\d{9}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的手机号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证身份证号\r\n    const validateIdCard = (rule, value, callback) => {\r\n      const pattern = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的身份证号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      isTaskTypeEdit: true,\r\n      vehicleEmissionStandardsOptions: [],\r\n      taskTypeOptions: [],\r\n      carList: [],\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n      driverList: [],\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n      //审核表单\r\n      approveForm: {\r\n        applyNo: null,\r\n        approveContent: '',//审核意见\r\n        approveFlag: true,//审核状态\r\n      },\r\n\r\n      // 图片列表\r\n      imageList: [],\r\n\r\n      // 文件列表\r\n      fileList: [],\r\n\r\n      // 派车弹框可见性\r\n      dispatchDialogVisible: false,\r\n\r\n      taskListInfo: [],\r\n\r\n      // 派车表单数据\r\n      dispatchForm: {\r\n        // carNumber: '',\r\n        // driverName: '',\r\n        // driverPhone: '',\r\n        // driverIdCard: ''\r\n      },\r\n\r\n      // 派车表单验证规则\r\n      dispatchRules: {\r\n        carNumber: [\r\n          { required: true, message: '请输入车牌号', trigger: 'blur' },\r\n          { validator: validateCarNumber, trigger: 'blur' }\r\n        ],\r\n        driverName: [\r\n          { required: true, message: '请输入司机姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }\r\n        ],\r\n        driverPhone: [\r\n          { required: true, message: '请输入司机手机号', trigger: 'blur' },\r\n          { validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        driverIdCard: [\r\n          { required: true, message: '请输入司机身份证号', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 派车列表数据\r\n      dispatchList: [\r\n        {\r\n          id: 1,\r\n          carNumber: '京A12345',\r\n          driverName: '王小明',\r\n          driverPhone: '13800138000',\r\n          driverIdCard: '110101199001010001',\r\n          dispatchTime: '2025-03-18 09:30:00',\r\n          status: 2,\r\n          tareWeight: 8500,\r\n          grossWeight: 15800,\r\n          recheckedGrossWeight: 15750,\r\n          recheckedTareWeight: 8480\r\n        },\r\n        {\r\n          id: 2,\r\n          carNumber: '京B98765',\r\n          driverName: '李大壮',\r\n          driverPhone: '13900139000',\r\n          driverIdCard: '110101199102020002',\r\n          dispatchTime: '2025-03-19 14:15:00',\r\n          status: 1,\r\n          tareWeight: 7800,\r\n          grossWeight: 12600,\r\n          recheckedGrossWeight: null,\r\n          recheckedTareWeight: null\r\n        }\r\n      ],\r\n\r\n      // 计划详情信息\r\n      planInfo: {},\r\n      applyNo: null,\r\n      taskQueryParams: {\r\n        applyNo: null,\r\n      },\r\n\r\n      taskMaterialList: null,\r\n      // 物资选择相关数据\r\n      materialSelectionList: [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        usedNum: 0,\r\n        remainingNum: 0,\r\n        currentNum: 0\r\n      }],\r\n      availableMaterials: [], // 可选的物资列表\r\n      taskMaterialListMap: new Map(), // 已派车的物资列表\r\n      taskMaterialMap: new Map(), // 存储所有任务物资的映射\r\n    };\r\n  },\r\n  computed: {\r\n    // 判断是否可以派车\r\n    canDispatchCar() {\r\n      // 判断申请单是否已通过\r\n      // const isPlanApproved = this.planInfo.planStatus === 2;\r\n\r\n      // // 如果是非计量类型，且已经派过车，则不能再派车\r\n      // if (this.planInfo.measureFlag !== 1 && this.dispatchList.length > 0) {\r\n      //   return false;\r\n      // }\r\n\r\n      return true;\r\n    },\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    },\r\n    canShowMaterialConfirm() {\r\n      // 只有planStatus为5或6时显示（已出厂/部分收货），且不是已完成/废弃/驳回/过期\r\n      return [5, 6].includes(this.planInfo.planStatus);\r\n    }\r\n  },\r\n  activated() {\r\n    this.getDicts(\"xctg_driver_car_emission_standards\").then(response => {\r\n      this.vehicleEmissionStandardsOptions = response.data;\r\n    });\r\n    // 初始化任务类型选项（在获取计划信息后会重新更新）\r\n    this.getDicts(\"leave_task_type\").then(response => {\r\n      this.taskTypeOptions = response.data;\r\n    });\r\n    // 获取路由参数中的ID\r\n    const applyNo = this.$route.params.applyNo;\r\n    this.applyNo = applyNo\r\n    this.taskQueryParams.applyNo = applyNo;\r\n    this.approveForm.applyNo = applyNo;\r\n    if (applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        this.planInfo = response.data;\r\n        this.taskTypeEditUpdate();\r\n        console.log(\"this.planInfo\", this.planInfo);\r\n        // 解析图片和文件数据\r\n        this.parseImageAndFileData();\r\n        // 获取任务信息后更新任务类型选项\r\n        this.getListTaskInfo();\r\n      });\r\n    };\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n\r\n\r\n  },\r\n\r\n\r\n  methods: {\r\n    handleApprove() {\r\n      this.approveForm.approveFlag = true;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('审核通过');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleReject() {\r\n      this.approveForm.approveFlag = false;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('驳回成功');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleDiscard() {\r\n      discard(this.planInfo).then(response => {\r\n        this.$message.success('废弃成功');\r\n\r\n        if (window.history.length > 1) {\r\n          this.$router.go(-1);\r\n        } else {\r\n          this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('废弃失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    taskTypeEditUpdate() {\r\n      if (this.planInfo.planType !== 2) {\r\n        this.isTaskTypeEdit = false;\r\n      }\r\n    },\r\n    // 更新任务类型选项\r\n    updateTaskTypeOptions() {\r\n      // 获取原始的任务类型选项\r\n      this.getDicts(\"leave_task_type\").then(response => {\r\n        let options = response.data;\r\n        console.log(\"原始任务类型选项:\", options);\r\n        console.log(\"计划类型:\", this.planInfo.planType);\r\n        console.log(\"任务数量:\", this.taskListInfo.length);\r\n\r\n        // 对于出厂返回任务（planType=2）\r\n        if (this.planInfo.planType === 2) {\r\n          // 如果当前任务数为0，只显示出厂选项\r\n          if (this.taskListInfo.length === 0) {\r\n            options = options.filter(option =>\r\n              option.dictValue === '1' || option.dictValue === 1\r\n            ); // 只保留出厂选项，兼容字符串和数字类型\r\n            console.log(\"过滤后只保留出厂选项:\", options);\r\n          }\r\n          // 如果已有任务，显示所有选项（出厂和返厂）\r\n        } else {\r\n          // 对于其他计划类型，保持原有逻辑\r\n          if (this.planInfo.planType !== 3) {\r\n            options = options.filter(option =>\r\n              option.dictValue !== '3' && option.dictValue !== 3\r\n            ); // 移除跨区调拨选项，兼容字符串和数字类型\r\n          }\r\n        }\r\n\r\n        console.log(\"最终任务类型选项:\", options);\r\n        this.taskTypeOptions = options;\r\n        // 强制刷新下拉框\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      });\r\n    },\r\n    getListTaskInfo() {\r\n      listAllTask(this.taskQueryParams).then(response => {\r\n        console.log(\"response.data\", response.rows);\r\n        this.taskListInfo = response.data;\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        // 获取所有任务物资\r\n        this.getAllTaskMaterials();\r\n        // 更新任务类型选项（基于当前任务数）\r\n        this.updateTaskTypeOptions();\r\n      });\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    // 1国五，2国六，3新能源字典翻译\r\n    vehicleEmissionStandardsFormat(row, column) {\r\n      return this.selectDictLabel(this.vehicleEmissionStandardsOptions, row.vehicleEmissionStandards);\r\n    },\r\n\r\n    taskTypeFormat(row, column) {\r\n      return this.getTaskTypeText(row.taskType);\r\n    },\r\n    taskStatusFormat(row, column) {\r\n      return this.getStatusText(row.taskStatus);\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarListByPage().then(response => {\r\n        this.carList = response.rows;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n        // 调用后端接口进行搜索\r\n        const searchParams = {\r\n          carNumber: query\r\n        };\r\n        getXctgDriverCarListByPage(searchParams).then(response => {\r\n          this.filteredCarOptions = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('搜索货车失败:', error);\r\n          this.filteredCarOptions = [];\r\n        });\r\n      } else {\r\n        // 如果没有搜索条件，显示前50条数据\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleDriverChange() {\r\n      if (this.dispatchForm.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.dispatchForm.driverId) {\r\n            this.dispatchForm.name = item.name;\r\n            this.dispatchForm.idCard = item.idCard;\r\n            this.dispatchForm.company = item.company;\r\n            this.dispatchForm.phone = item.phone;\r\n            this.dispatchForm.photo = item.photo;\r\n            this.dispatchForm.faceImgList = item.faceImgList;\r\n            this.dispatchForm.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.dispatchForm.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n            this.dispatchForm.sex = item.gender;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //通过carUUId获取车辆信息\r\n    handleCarChange() {\r\n      console.log(\"handleCarChange - 选择的车辆ID:\", this.dispatchForm.carUUId)\r\n      if (this.dispatchForm.carUUId != null) {\r\n        // 优先在filteredCarOptions中查找（包含搜索结果）\r\n        let selectedCar = this.filteredCarOptions.find(item => item.id == this.dispatchForm.carUUId);\r\n\r\n        // 如果在filteredCarOptions中没找到，再在carList中查找\r\n        if (!selectedCar) {\r\n          selectedCar = this.carList.find(item => item.id == this.dispatchForm.carUUId);\r\n        }\r\n\r\n        if (selectedCar) {\r\n          console.log(\"找到车辆信息:\", selectedCar);\r\n          this.dispatchForm.carNumber = selectedCar.carNumber;\r\n\r\n          if (selectedCar.vehicleEmissionStandards == 1) {\r\n            this.dispatchForm.vehicleEmissionStandards = \"国五\";\r\n          } else if (selectedCar.vehicleEmissionStandards == 2) {\r\n            this.dispatchForm.vehicleEmissionStandards = \"国六\";\r\n          } else if (selectedCar.vehicleEmissionStandards == 3) {\r\n            this.dispatchForm.vehicleEmissionStandards = \"新能源\";\r\n          } else {\r\n            this.dispatchForm.vehicleEmissionStandards = \"\";\r\n          }\r\n          this.dispatchForm.licensePlateColor = selectedCar.licensePlateColor;\r\n          this.dispatchForm.carId = selectedCar.carId;\r\n          this.dispatchForm.trailerNumber = selectedCar.trailerNumber;\r\n          this.dispatchForm.trailerId = selectedCar.trailerId;\r\n          this.dispatchForm.axisType = selectedCar.axisType;\r\n          this.dispatchForm.driverWeight = selectedCar.driverWeight;\r\n          this.dispatchForm.maxWeight = selectedCar.maxWeight;\r\n          this.dispatchForm.engineNumber = selectedCar.engineNumber;\r\n          this.dispatchForm.vinNumber = selectedCar.vinNumber;\r\n\r\n          console.log(\"车辆信息已更新:\", {\r\n            carNumber: this.dispatchForm.carNumber,\r\n            vehicleEmissionStandards: this.dispatchForm.vehicleEmissionStandards\r\n          });\r\n        } else {\r\n          console.warn(\"未找到选中的车辆信息，carUUId:\", this.dispatchForm.carUUId);\r\n          // 清空相关字段\r\n          this.dispatchForm.carNumber = \"\";\r\n          this.dispatchForm.vehicleEmissionStandards = \"\";\r\n        }\r\n      }\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserListByPage().then(response => {\r\n        this.driverList = response.rows;\r\n        console.log(\"this.driverList\", this.driverList);\r\n        this.filteredDriverOptions = this.driverList;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n        // 调用后端接口进行搜索\r\n        const searchParams = {\r\n          searchValue: query\r\n        };\r\n        getXctgDriverUserListByPage(searchParams).then(response => {\r\n          this.filteredDriverOptions = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('搜索货车司机失败:', error);\r\n          this.filteredDriverOptions = [];\r\n        });\r\n      } else {\r\n        // 如果没有搜索条件，显示前50条数据\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n    // 解析图片和文件数据\r\n    parseImageAndFileData() {\r\n      // 解析图片数据\r\n      if (this.planInfo.applyImgUrl) {\r\n        try {\r\n          this.imageList = JSON.parse(this.planInfo.applyImgUrl);\r\n        } catch (e) {\r\n          console.error('解析图片数据失败:', e);\r\n          this.imageList = [];\r\n        }\r\n      }\r\n\r\n      // 解析文件数据\r\n      if (this.planInfo.applyFileUrl) {\r\n        try {\r\n          this.fileList = JSON.parse(this.planInfo.applyFileUrl);\r\n        } catch (e) {\r\n          console.error('解析文件数据失败:', e);\r\n          this.fileList = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(url, fileName) {\r\n      if (!url) {\r\n        this.$message.error('文件链接无效');\r\n        return;\r\n      }\r\n\r\n      // 创建一个a元素用于下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    // 获取计划类型文本\r\n    getPlanTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂不返回',\r\n        2: '出厂返回',\r\n        3: '跨区调拨',\r\n        4: '退货申请'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取业务类型文本\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用',\r\n        11: '通用',\r\n        12: '委外加工',\r\n        21: '有计划量计量',\r\n        22: '短期',\r\n        23: '钢板（圆钢）',\r\n        31: '通用'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n\r\n    // 获取物资类型文本\r\n    getMaterialTypeText(type) {\r\n      const typeMap = {\r\n        1: '钢材',\r\n        2: '钢板',\r\n        3: '其他'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取计划状态文本\r\n    getPlanStatusText(status) {\r\n      const statusMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#67C23A', // 审批\r\n        3: '#E6A23C', // 流转\r\n        4: '#F56C6C', // 驳回\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.logType] || '#409EFF';\r\n    },\r\n\r\n    // 获取派车状态文本\r\n    getDispatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '待出发',\r\n        1: '已出发',\r\n        2: '已到达',\r\n        3: '已完成',\r\n        4: '已取消'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取派车状态类型（用于标签颜色）\r\n    getDispatchStatusType(status) {\r\n      const statusMap = {\r\n        0: 'info',\r\n        1: 'primary',\r\n        2: 'success',\r\n        3: 'success',\r\n        4: 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取计划类型标签样式\r\n    getPlanTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'success',  // 出厂不返回\r\n        2: 'warning',  // 出厂返回\r\n        3: 'info',     // 跨区调拨\r\n        4: 'danger'    // 退货申请\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取物资类型标签样式\r\n    getMaterialTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'primary',  // 钢材\r\n        2: 'success',  // 钢板\r\n        3: 'info'      // 其他\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取业务类型标签样式\r\n    getBusinessCategoryTagType(category) {\r\n      const typeMap = {\r\n        '1': 'primary',   // 通用\r\n        '11': 'primary',  // 通用\r\n        '12': 'warning',  // 委外加工\r\n        '21': 'success',  // 有计划量计量\r\n        '22': 'info',     // 短期\r\n        '23': 'danger',   // 钢板（圆钢）\r\n        '31': 'primary'   // 通用\r\n      };\r\n      return typeMap[category] || 'info';\r\n    },\r\n\r\n    // 打开派车弹框\r\n    openDispatchDialog() {\r\n      // 初始化物资数据\r\n      this.availableMaterials = this.planInfo.materials || [];\r\n      console.log(\"this.availableMaterials\", this.availableMaterials);\r\n\r\n      // 获取已派车的物资列表，并在回调中初始化 materialSelectionList\r\n      this.getTaskMaterialListAndInitSelection();\r\n      // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.supplier') && !roles.includes('leave.applicant')) {\r\n        this.$message.error('您没有派车权限');\r\n        return;\r\n      }\r\n\r\n      console.log(\"this.planInfo.planStatus\", this.planInfo.planStatus);\r\n      if (![4, 5, 6].includes(this.planInfo.planStatus)) {\r\n        this.$message.warning('当前状态无法派车');\r\n        return;\r\n      }\r\n\r\n\r\n\r\n\r\n      console.log(\"openDispatchDialog\", this.taskListInfo.length);\r\n      if (this.planInfo.businessCategory == 22 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('短期计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      if (this.planInfo.businessCategory == 23 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('钢板（圆钢）计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      this.dispatchForm = {};\r\n\r\n      // 更新任务类型选项\r\n      this.updateTaskTypeOptions();\r\n\r\n      if (this.planInfo.planType == 1) {\r\n        this.dispatchForm.taskType = \"1\"\r\n      } else if (this.planInfo.planType == 3) {\r\n        this.dispatchForm.taskType = \"3\"\r\n      } else if (this.planInfo.planType == 4) {\r\n        this.dispatchForm.taskType = \"1\"\r\n      } else if (this.planInfo.planType == 2) {\r\n        // 对于出厂返回任务，根据当前任务数决定默认任务类型\r\n        if (this.taskListInfo.length === 0) {\r\n          this.dispatchForm.taskType = \"1\"; // 默认选择出厂\r\n        } else {\r\n          this.dispatchForm.taskType = \"2\"; // 默认选择返厂\r\n        }\r\n      }\r\n      console.log(this.dispatchForm.taskType),\r\n        this.dispatchDialogVisible = true;\r\n\r\n\r\n    },\r\n\r\n    // 新增方法\r\n    getTaskMaterialListAndInitSelection() {\r\n      // 清空已用数量映射\r\n      this.taskMaterialListMap.clear();\r\n      // 统计所有已派车物资\r\n      const type2List = this.taskListInfo.filter(item => item.taskType === 2);\r\n      console.log(\"type2List\", type2List);\r\n      if (!type2List || type2List.length === 0) {\r\n        // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n        this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n          // const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n          // const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n          return {\r\n            materialId: mat.materialId,\r\n            materialName: mat.materialName,\r\n            materialSpec: mat.materialSpec,\r\n            planNum: mat.planNum,\r\n            usedNum: 0,\r\n            remainingNum: mat.planNum,\r\n            currentNum: mat.planNum\r\n          };\r\n        });\r\n      } else {\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        type2List.forEach(task => {\r\n          const params = { taskNo: task.taskNo };\r\n          listTaskMaterial(params).then(response => {\r\n            let taskMaterials = response.rows || [];\r\n            taskMaterials.forEach(material => {\r\n              if (!this.taskMaterialListMap.has(material.materialId)) {\r\n                this.taskMaterialListMap.set(material.materialId, {\r\n                  taskMaterialInfo: material,\r\n                  usedNum: material.planNum\r\n                });\r\n              } else {\r\n                const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n                existingMaterial.usedNum += material.planNum;\r\n              }\r\n            });\r\n\r\n            // 将taskMaterialListMap转换为数组集合\r\n            this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n              materialId: key,\r\n              ...value\r\n            }));\r\n\r\n            // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n            this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n              const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n              const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n\r\n              return {\r\n                materialId: mat.materialId,\r\n                materialName: mat.materialName,\r\n                materialSpec: mat.materialSpec,\r\n                planNum: mat.planNum,\r\n                usedNum: usedNum,\r\n                remainingNum: remainingNum,\r\n                currentNum: remainingNum\r\n              };\r\n            });\r\n\r\n            this.materialSelectionList = this.materialSelectionList.filter(item => item.remainingNum > 0);\r\n          });\r\n        });\r\n      }\r\n\r\n         // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n    },\r\n\r\n    // 重置派车表单\r\n    resetDispatchForm() {\r\n      this.$refs.dispatchForm && this.$refs.dispatchForm.resetFields();\r\n      this.materialSelectionList = [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      }];\r\n    },\r\n\r\n    // 获取已派车的物资列表\r\n    getTaskMaterialList() {\r\n      // 从taskListInfo中获取已派车的物资信息\r\n      this.taskMaterialListMap.clear();\r\n      this.taskListInfo.forEach(task => {\r\n        const params = {\r\n          taskNo: task.taskNo,\r\n        };\r\n        listTaskMaterial(params).then(response => {\r\n          console.log(\"listTaskMaterial\", response.rows);\r\n          let taskMaterials = [];\r\n          taskMaterials = response.rows;\r\n          taskMaterials.forEach(material => {\r\n            if (!this.taskMaterialListMap.has(material.materialId)) {\r\n              this.taskMaterialListMap.set(material.materialId, {\r\n                taskMaterialInfo: material,\r\n                usedNum: material.planNum\r\n              });\r\n            } else {\r\n              const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n              existingMaterial.usedNum += material.planNum;\r\n            }\r\n          });\r\n          // 将taskMaterialListMap转换为数组集合\r\n          this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n            materialId: key,\r\n            ...value\r\n          }));\r\n          console.log(\"taskMaterialArray\", this.taskMaterialList);\r\n          console.log(\"taskMaterialListMap\", this.taskMaterialListMap);\r\n        });\r\n      });\r\n    },\r\n\r\n    // 添加物资行\r\n    addMaterialRow() {\r\n      this.materialSelectionList.push({\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      });\r\n    },\r\n\r\n    // 移除物资行\r\n    removeMaterial(index) {\r\n      this.materialSelectionList.splice(index, 1);\r\n    },\r\n\r\n    // 处理物资选择变化\r\n    handleMaterialChange(row, index) {\r\n      console.log(\"handleMaterialChange\", this.taskMaterialList);\r\n\r\n\r\n      const selectedMaterial = this.taskMaterialList.find(item => item.materialId === row.materialId);\r\n      if (selectedMaterial) {\r\n        row.usedNum = selectedMaterial.usedNum;\r\n      }\r\n      const selectPlanMaterial = this.planInfo.materials.find(item => item.materialId === row.materialId);\r\n\r\n      if (selectPlanMaterial) {\r\n        row.planNum = selectPlanMaterial.planNum;\r\n        row.materialName = selectPlanMaterial.materialName;\r\n        row.materialSpec = selectPlanMaterial.materialSpec;\r\n      }\r\n\r\n      row.remainingNum = row.planNum - row.usedNum;\r\n      row.currentNum = row.planNum - row.usedNum;\r\n\r\n      console.log(\"handleMaterialChange\", row, index);\r\n\r\n    },\r\n\r\n    // 获取物资最大可用数量\r\n    getMaxAvailableNum(row) {\r\n      console.log(111);\r\n      if (!row.materialId) return 0;\r\n\r\n      // 从taskMaterialListMap中获取已用数量\r\n      const materialInfo = this.taskMaterialListMap.get(row.materialId);\r\n      const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      return row.planNum - usedNum;\r\n    },\r\n\r\n    // 处理数量变化\r\n    handleNumChange(value, index) {\r\n      console.log(222);\r\n      if (!this.materialSelectionList[index]) return;\r\n\r\n      const row = this.materialSelectionList[index];\r\n      if (!row.materialId) return;\r\n\r\n      // 从taskMaterialListMap中获取已用数量\r\n      const materialInfo = this.taskMaterialListMap.get(row.materialId);\r\n      const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      // 计算最大可用数量\r\n      const maxAvailable = row.planNum - usedNum;\r\n\r\n      // 如果当前值大于最大可用数量，则将值置为最大可用数量\r\n      if (value > maxAvailable) {\r\n        // 显示提示消息\r\n        this.$message({\r\n          message: `数量已自动调整为最大可用数量：${maxAvailable}`,\r\n          type: 'warning',\r\n          duration: 3000,\r\n          showClose: true\r\n        });\r\n\r\n        this.$nextTick(() => {\r\n          row.currentNum = maxAvailable;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    isMaterialAvailable(material) {\r\n      // 从taskMaterialListMap中获取已用数量\r\n      // const materialInfo = this.taskMaterialListMap.get(material.id);\r\n      // const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      // let selected = false;\r\n\r\n      // this.availableMaterials.forEach(item => {\r\n      //   if (item.materialId === material.materialId) {\r\n      //     selected = true;\r\n      //   }\r\n      // });\r\n\r\n      return this.materialSelectionList.some(row => row.materialId === material.materialId);;\r\n    },\r\n\r\n    // 修改提交派车表单方法\r\n    submitDispatchForm() {\r\n      this.$refs.dispatchForm.validate(valid => {\r\n        if (valid) {\r\n          // 判断非计量且taskType为1的情况\r\n          if (this.planInfo.measureFlag == 0) {\r\n            if (this.dispatchForm.taskType == 1) {\r\n              // 检查是否已经有taskType为1的任务\r\n              const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n              if (hasType1Task) {\r\n                this.$message.warning('非计量只能派车出厂一次');\r\n                return;\r\n              }\r\n            }\r\n          }\r\n\r\n          // 新集合\r\n          let resultList = [];\r\n\r\n          console.log(\"this.planInfo.measureFlag\", this.planInfo.measureFlag);\r\n          console.log(\"this.dispatchForm.taskType\", this.dispatchForm.taskType);\r\n\r\n          if (this.planInfo.measureFlag == 0 && this.dispatchForm.taskType == 2) {\r\n            this.materialSelectionList.forEach(selRow => {\r\n              // 在 planInfo.materials 中查找相同 materialId 的元素\r\n              const planMaterial = (this.planInfo.materials || []).find(\r\n                mat => mat.materialId === selRow.materialId\r\n              );\r\n              if (planMaterial) {\r\n                // 深拷贝一份，避免影响原数据\r\n                const newItem = { ...planMaterial };\r\n                newItem.planNum = selRow.currentNum; // 设置为本次数量\r\n                resultList.push(newItem);\r\n              }\r\n            });\r\n\r\n            // resultList 即为你需要的新集合\r\n            console.log('this.materialSelectionList', this.materialSelectionList);\r\n            console.log('resultList', resultList);\r\n\r\n            // 物资校验：必须有物资\r\n            if (!this.materialSelectionList.length) {\r\n              this.$message.warning('请至少选择一种物资');\r\n              return;\r\n            }\r\n\r\n            // 校验每一行物资\r\n            const hasInvalidMaterial = this.materialSelectionList.some(row => {\r\n              // 必须选择物资，数量>0，且数量<=剩余数量\r\n              return (\r\n                !row.materialId ||\r\n                row.currentNum <= 0 ||\r\n                row.currentNum > row.remainingNum\r\n              );\r\n            });\r\n\r\n            if (hasInvalidMaterial) {\r\n              this.$message.warning('请选择物资且本次数量需大于0且不超过剩余数量');\r\n              return;\r\n            }\r\n          } else {\r\n            console.log(\"this.planInfo.materials\", this.planInfo.materials);\r\n            resultList = this.planInfo.materials ? this.planInfo.materials.map(item => ({ ...item })) : [];\r\n            console.log(\"123321\", resultList);\r\n          }\r\n\r\n\r\n\r\n\r\n\r\n          if (this.planInfo.measureFlag == 1 && this.dispatchForm.taskType !== 2) {\r\n            this.dispatchForm.taskStatus = 1;\r\n          } else {\r\n            this.dispatchForm.taskStatus = 4;\r\n          }\r\n\r\n          if (this.dispatchForm.taskType == 2) {\r\n            this.dispatchForm.taskStatus = 5;\r\n          }\r\n\r\n\r\n          //是否直供默认为0\r\n          this.dispatchForm.isDirectSupply = 0;\r\n          // todo 任务状态确认\r\n          this.dispatchForm.applyNo = this.applyNo;\r\n          this.dispatchForm.planNo = this.planInfo.planNo;\r\n          this.dispatchForm.carNum = this.dispatchForm.carNumber;\r\n          this.dispatchForm.companyName = this.dispatchForm.company;\r\n          this.dispatchForm.driverLicenseImg = this.dispatchForm.driverLicenseImgs;\r\n          this.dispatchForm.driverName = this.dispatchForm.name;\r\n          this.dispatchForm.mobilePhone = this.dispatchForm.phone;\r\n          this.dispatchForm.faceImg = this.dispatchForm.photo;\r\n          this.dispatchForm.drivingLicenseImg = this.dispatchForm.vehicleLicenseImgs;\r\n          this.dispatchForm.idCardNo = this.dispatchForm.idCard;\r\n          if (this.dispatchForm.sex == \"1\") {\r\n            this.dispatchForm.sex = 1;\r\n          } else if (this.dispatchForm.sex == \"2\") {\r\n            this.dispatchForm.sex = 2;\r\n          }\r\n          if (this.dispatchForm.vehicleEmissionStandards == \"国五\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 1;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"国六\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 2;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"新能源\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 3;\r\n          }\r\n          console.log(\"提交派车表单 - dispatchForm:\", this.dispatchForm);\r\n          console.log(\"车牌号信息:\", {\r\n            carNumber: this.dispatchForm.carNumber,\r\n            carNum: this.dispatchForm.carNum\r\n          });\r\n\r\n          let dispatchInfo = {};\r\n          dispatchInfo.carNum = this.dispatchForm.carNum;\r\n          console.log(\"调用isAllowDispatch接口 - 传递参数:\", dispatchInfo);\r\n\r\n          isAllowDispatch(dispatchInfo).then(response => {\r\n            let row = response.data;\r\n            if (row > 0) {\r\n              this.$message.error(\"当前车有正在执行的任务\")\r\n            } else {\r\n              let param = {};\r\n              param.leaveTask = this.dispatchForm;\r\n              param.leaveTaskMaterialList = resultList;\r\n              addTaskAndMaterialAndAddLeaveLog(param).then(res => {\r\n                console.log(\"addTaskAndMaterialAndAddLeaveLog\", res)\r\n                if (res.code == 200) {\r\n                  this.$message.success('派车成功');\r\n                  this.dispatchDialogVisible = false;\r\n                  this.getListTaskInfo();\r\n                } else {\r\n                  // 其他失败原因\r\n                  this.$message.error(res.message || '派车失败');\r\n                }\r\n              }).catch(err => {\r\n                console.error('dispatch error:', err);\r\n                this.$message.error('网络异常，稍后重试');\r\n              });\r\n\r\n              // addTaskAndMaterial(this.dispatchForm).then(response => {\r\n              //   console.log(\"addTaskAndMaterial\", response);\r\n              //   let snowId = response.data;\r\n              //   this.planInfo.materials.forEach(item => {\r\n              //     item.taskNo = snowId;\r\n              //     addTaskMaterial(item);\r\n              //   });\r\n\r\n              //   console.log(\"生成派车日志\");\r\n\r\n              //   //生成派车日志\r\n              //   let leaveTaskLog = {};\r\n\r\n\r\n              //   leaveTaskLog.logType = 2;\r\n              //   leaveTaskLog.taskNo = snowId;\r\n              //   leaveTaskLog.applyNo = this.applyNo;\r\n              //   leaveTaskLog.info = '派车任务创建：' + this.dispatchForm.carNum + ' ' + this.dispatchForm.driverName\r\n              //   addLeaveLog(leaveTaskLog);\r\n\r\n              //   this.$message.success('派车成功');\r\n              //   this.dispatchDialogVisible = false;\r\n              //   this.getListTaskInfo();\r\n              // });\r\n\r\n              this.dispatchDialogVisible = false;\r\n            }\r\n            console.log(\"this.isAllowDispatch\", response);\r\n          }).catch(err => {\r\n            console.error('dispatch error:', err);\r\n            this.$message.error('网络异常，稍后重试');\r\n          });\r\n\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(date) {\r\n      const year = date.getFullYear();\r\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const day = date.getDate().toString().padStart(2, '0');\r\n      const hours = date.getHours().toString().padStart(2, '0');\r\n      const minutes = date.getMinutes().toString().padStart(2, '0');\r\n      const seconds = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 打印功能\r\n    handlePrint() {\r\n      this.$message.success('打印功能尚未实现');\r\n      // 实际项目中可以调用浏览器打印功能\r\n      // window.print();\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$tab.closeOpenPage(this.$route);\r\n      this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n    },\r\n\r\n    // 跳转到任务详情页面\r\n    goToTaskDetail(row) {\r\n      this.$router.push({\r\n        path: `/leave/plan/task/${row.taskNo}`\r\n      });\r\n    },\r\n\r\n    getTaskTypeText(taskType) {\r\n      const standardMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return standardMap[taskType] || '未知';\r\n    },\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取计划状态类型\r\n    getPlanStatusType(status) {\r\n      const statusMap = {\r\n        '1': 'warning',  // 待分厂审批\r\n        '2': 'warning',  // 待分厂复审\r\n        '3': 'warning',  // 待生产指挥中心审批\r\n        '4': 'success',  // 审批完成\r\n        '5': 'primary',  // 已出厂\r\n        '6': 'info',     // 部分收货\r\n        '7': 'success',  // 已完成\r\n        '11': 'danger',  // 驳回\r\n        '12': 'danger',  // 废弃\r\n        '13': 'danger'   // 过期\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /**\r\n     * 获取计划下所有任务的任务物资\r\n     * @returns {Promise<void>}\r\n     */\r\n    async getAllTaskMaterials() {\r\n      try {\r\n        // 清空现有数据\r\n        this.taskMaterialMap.clear();\r\n\r\n        // 获取该计划下所有任务的任务物资\r\n        const params = {\r\n          applyNo: this.applyNo\r\n        };\r\n\r\n        const response = await listTaskMaterial(params);\r\n        if (response.code === 200 && response.rows) {\r\n          // 将任务物资按物资ID分组存储\r\n          response.rows.forEach(material => {\r\n            const key = material.materialId;\r\n            if (!this.taskMaterialMap.has(key)) {\r\n              this.taskMaterialMap.set(key, {\r\n                materialId: material.materialId,\r\n                materialName: material.materialName,\r\n                materialSpec: material.materialSpec,\r\n                planNum: material.planNum,\r\n                usedNum: 0,\r\n                taskMaterials: [] // 存储每个任务的具体物资信息\r\n              });\r\n            }\r\n\r\n            const materialInfo = this.taskMaterialMap.get(key);\r\n            // 累加每个任务物资的计划数量作为已使用数量\r\n            materialInfo.usedNum += material.planNum;\r\n            materialInfo.taskMaterials.push({\r\n              taskNo: material.taskNo,\r\n              carNum: material.carNum,\r\n              planNum: material.planNum,\r\n              createTime: material.createTime\r\n            });\r\n          });\r\n        }\r\n\r\n        // 更新物资选择列表中的已使用数量\r\n        this.updateMaterialUsedNum();\r\n\r\n        console.log('Task Material Map:', this.taskMaterialMap);\r\n      } catch (error) {\r\n        console.error('获取任务物资失败:', error);\r\n        this.$message.error('获取任务物资失败');\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 更新物资选择列表中的已使用数量\r\n     */\r\n    updateMaterialUsedNum() {\r\n      this.materialSelectionList.forEach(row => {\r\n        if (row.materialId) {\r\n          const materialInfo = this.taskMaterialMap.get(row.materialId);\r\n          if (materialInfo) {\r\n            // 直接使用累加的计划数量作为已使用数量\r\n            row.usedNum = materialInfo.usedNum;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 物资确认按钮点击事件\r\n    async handleMaterialConfirm() {\r\n      try {\r\n        // 校验所有任务的taskStatus是否为9\r\n        if (this.taskListInfo && this.taskListInfo.length > 0) {\r\n          const unfinishedTasks = this.taskListInfo.filter(task => task.taskStatus !== 9);\r\n          if (unfinishedTasks.length > 0) {\r\n            this.$message.error('存在未完成的任务，无法进行物资确认');\r\n            return;\r\n          }\r\n        }\r\n\r\n        // 调用后端接口，传递applyNo\r\n        await confirmMaterial({ applyNo: this.applyNo });\r\n        this.$message.success('物资确认成功');\r\n        // 刷新详情\r\n        this.getListTaskInfo();\r\n        // 重新获取planInfo\r\n        detailPlan(this.applyNo).then(response => {\r\n          this.planInfo = response.data;\r\n        });\r\n      } catch (e) {\r\n        this.$message.error('物资确认失败');\r\n      }\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #8957e5;\r\n  /* 基本信息模块 - 紫色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 图片列表模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 文件列表模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 物资列表模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 派车信息模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(6) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #8957e5;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(6) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.fixed-bottom-action {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 15px 0;\r\n  text-align: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 20px 30px 0;\r\n}\r\n\r\n.image-container,\r\n.file-container {\r\n  padding: 20px;\r\n}\r\n\r\n.image-list,\r\n.file-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.image-item {\r\n  width: 150px;\r\n  height: 180px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 150px;\r\n  object-fit: cover;\r\n  display: block;\r\n}\r\n\r\n.image-name {\r\n  padding: 5px;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  min-width: 180px;\r\n  max-width: 250px;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 24px;\r\n  margin-right: 8px;\r\n  color: #909399;\r\n}\r\n\r\n.file-item:hover .file-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 新增物资选择相关样式 */\r\n.el-input-number {\r\n  width: 120px;\r\n}\r\n\r\n.material-selection {\r\n  margin-top: 20px;\r\n}\r\n\r\n.material-selection .el-table {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.dispatch-log-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 0px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.el-button--text {\r\n  color: #409EFF;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n\r\n  &:hover {\r\n    color: #66b1ff;\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:focus {\r\n    color: #409EFF;\r\n  }\r\n}\r\n</style>\r\n"]}]}